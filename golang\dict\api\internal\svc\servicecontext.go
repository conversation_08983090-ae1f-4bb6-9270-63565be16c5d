package svc

import (
	"api/internal/config"
	"dict_category_rpc/dictcategoryservice"
	"dict_item_rpc/dictitemservice"
	"dict_rpc/dictservice"

	"github.com/zeromicro/go-zero/zrpc"
)

type ServiceContext struct {
	Config          config.Config
	DictRpc         dictservice.DictService
	DictCategoryRpc dictcategoryservice.DictCategoryService
	DictItemRpc     dictitemservice.DictItemService
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config:          c,
		DictRpc:         dictservice.NewDictService(zrpc.MustNewClient(c.DictRpc)),
		DictCategoryRpc: dictcategoryservice.NewDictCategoryService(zrpc.MustNewClient(c.DictCategoryRpc)),
		DictItemRpc:     dictitemservice.NewDictItemService(zrpc.MustNewClient(c.DictItemRpc)),
	}
}
