package logic

import (
	"context"

	"dict_item_rpc/dict_item"
	"dict_item_rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDictItemLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDictItemLogic {
	return &CreateDictItemLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 创建字典项
func (l *CreateDictItemLogic) CreateDictItem(in *dict_item.CreateDictItemReq) (*dict_item.CreateDictItemResp, error) {
	// todo: add your logic here and delete this line

	return &dict_item.CreateDictItemResp{}, nil
}
