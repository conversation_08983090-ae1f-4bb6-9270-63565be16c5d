package dict

import (
	"context"
	"dict_rpc/dict"
	"fmt"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDictLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 创建字典
func NewCreateDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDictLogic {
	return &CreateDictLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateDictLogic) CreateDict(req *types.CreateDictReq) (resp *types.CreateDictResp, err error) {
	// todo: add your logic here and delete this line
	// 连接dict_rpc服务
	dictResp, err := l.svcCtx.DictRpc.CreateDict(l.ctx, &dict.CreateDictReq{
		Code:   req.Code,
		Name:   req.Name,
		Remark: req.Remark,
		Status: req.Status,
	})

	if err != nil {
		return nil, fmt.Errorf("调用dict_rpc服务的方法CreateDict失败: %v",err)
	}

	return &types.CreateDictResp{
		Id:      dictResp.Id,
		Message: "创建字典成功",
	}, nil
}
