syntax = "proto3";

package dict_item;

option go_package = "./dict_item";

// 字典项信息
message DictItem {
  int64 id = 1;
  int64 dict_id = 2;
  int64 category_id = 3;
  string code = 4;
  string name = 5;
  int32 status = 6;
  string created_time = 7;
  string updated_time = 8;
}

// 创建字典项请求
message CreateDictItemReq {
  int64 dict_id = 1;
  int64 category_id = 2;
  string code = 3;
  string name = 4;
  int32 status = 5;
}

// 创建字典项响应
message CreateDictItemResp {
  int64 id = 1;
  string message = 2;
}

// 更新字典项请求
message UpdateDictItemReq {
  int64 id = 1;
  int64 dict_id = 2;
  int64 category_id = 3;
  string code = 4;
  string name = 5;
  int32 status = 6;
}

// 更新字典项响应
message UpdateDictItemResp {
  bool success = 1;
  string message = 2;
}

// 删除字典项请求
message DeleteDictItemReq {
  int64 id = 1;
}

// 删除字典项响应
message DeleteDictItemResp {
  bool success = 1;
  string message = 2;
}

// 获取字典项详情请求
message GetDictItemReq {
  int64 id = 1;
}

// 获取字典项详情响应
message GetDictItemResp {
  DictItem item = 1;
  string message = 2;
}

// 字典项列表请求
message ListDictItemReq {
  int32 page = 1;
  int32 page_size = 2;
  int64 dict_id = 3; // 指定字典ID
  int64 category_id = 4; // 指定分类ID
  string code = 5; // 模糊查询
  string name = 6; // 模糊查询
  int32 status = 7; // -1:全部, 0:禁用, 1:启用
}

// 字典项列表响应
message ListDictItemResp {
  int64 total = 1;
  repeated DictItem list = 2;
  string message = 3;
}

// 字典项服务
service DictItemService {
  // 创建字典项
  rpc CreateDictItem(CreateDictItemReq) returns(CreateDictItemResp);
  // 更新字典项
  rpc UpdateDictItem(UpdateDictItemReq) returns(UpdateDictItemResp);
  // 删除字典项
  rpc DeleteDictItem(DeleteDictItemReq) returns(DeleteDictItemResp);
  // 获取字典项详情
  rpc GetDictItem(GetDictItemReq) returns(GetDictItemResp);
  // 字典项列表
  rpc ListDictItem(ListDictItemReq) returns(ListDictItemResp);
} 