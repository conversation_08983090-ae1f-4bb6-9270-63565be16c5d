package model

import (
	"errors"
	"strconv"
	"time"

	"gorm.io/gorm"
)

// DictCategory 字典分类模型
type DictCategory struct {
	ID          int64          `gorm:"primaryKey;autoIncrement"`
	DictID      int64          `gorm:"not null;index"`
	Name        string         `gorm:"type:varchar(100);not null"`
	Status      int32          `gorm:"type:tinyint;not null;default:1;comment:'状态 0:禁用 1:启用'"`
	CreatedTime time.Time      `gorm:"not null;autoCreateTime"`
	UpdatedTime time.Time      `gorm:"not null;autoUpdateTime"`
	DeletedAt   gorm.DeletedAt `gorm:"index"`
}

// TableName 设置表名
func (DictCategory) TableName() string {
	return "dict_category"
}

// DictCategoryModel 字典分类模型结构
type DictCategoryModel struct {
	db *gorm.DB
}

// NewDictCategoryModel 创建字典分类模型
func NewDictCategoryModel(db *gorm.DB) *DictCategoryModel {
	return &DictCategoryModel{
		db: db,
	}
}

// FindByID 根据ID查询字典分类
func (m *DictCategoryModel) FindByID(id int64) (*DictCategory, error) {
	var category DictCategory
	result := m.db.First(&category, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("字典分类id=" + strconv.Itoa(int(id)) + "不存在")
		}
		return nil, result.Error
	}
	return &category, nil
}

// FindByDictID 根据字典ID查询分类列表
func (m *DictCategoryModel) FindByDictID(dictID int64) ([]*DictCategory, error) {
	var categories []*DictCategory
	result := m.db.Where("dict_id = ? AND status = 1", dictID).Order("created_time ASC").Find(&categories)
	if result.Error != nil {
		return nil, result.Error
	}
	return categories, nil
}

// Create 创建字典分类
func (m *DictCategoryModel) Create(category *DictCategory) error {
	return m.db.Create(category).Error
}

// Update 更新字典分类
func (m *DictCategoryModel) Update(category *DictCategory) error {
	return m.db.Save(category).Error
}

// Delete 删除字典分类
func (m *DictCategoryModel) Delete(id int64) error {
	return m.db.Delete(&DictCategory{}, id).Error
}

// List 分页查询字典分类列表
func (m *DictCategoryModel) List(page, pageSize int32, name string, status int32, dictID int64) ([]*DictCategory, int64, error) {
	var categories []*DictCategory
	var total int64

	query := m.db.Model(&DictCategory{})

	// 条件查询
	if name != "" {
		query = query.Where("name LIKE ?", "%"+name+"%")
	}
	if status != -1 {
		query = query.Where("status = ?", status)
	}
	if dictID > 0 {
		query = query.Where("dict_id = ?", dictID)
	}

	// 统计总数
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// 分页查询
	offset := int((page - 1) * pageSize)
	if err := query.Offset(offset).Limit(int(pageSize)).Order("created_time DESC").Find(&categories).Error; err != nil {
		return nil, 0, err
	}

	return categories, total, nil
}

// CheckNameExists 检查分类名称在指定字典下是否存在
func (m *DictCategoryModel) CheckNameExists(dictID int64, name string, excludeID int64) (bool, error) {
	var count int64
	query := m.db.Model(&DictCategory{}).Where("dict_id = ? AND name = ?", dictID, name)
	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}
	if err := query.Count(&count).Error; err != nil {
		return false, err
	}
	return count > 0, nil
}
