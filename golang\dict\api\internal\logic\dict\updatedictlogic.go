package dict

import (
	"context"
	"fmt"

	"api/internal/svc"
	"api/internal/types"
	"dict_rpc/dict"
	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateDictLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 更新字典
func NewUpdateDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDictLogic {
	return &UpdateDictLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateDictLogic) UpdateDict(req *types.UpdateDictReq) (resp *types.UpdateDictResp, err error) {
	// todo: add your logic here and delete this line
	// 调用dict_rpc服务
	respRpc, err := l.svcCtx.DictRpc.UpdateDict(l.ctx, &dict.UpdateDictReq{
		Id:     req.Id,
		Code:   req.Code,
		Name:   req.Name,
		Remark: req.Remark,
		Status: req.Status,
	})

	if err != nil {
		return nil, fmt.Errorf("调用dict_rpc服务的UpdateDict方法失败: %v", err)
	}

	return &types.UpdateDictResp{
		Success: respRpc.Success,
		Message: "更新字典成功",
	}, nil
}
