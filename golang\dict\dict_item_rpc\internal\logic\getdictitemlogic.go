package logic

import (
	"context"

	"dict_item_rpc/dict_item"
	"dict_item_rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDictItemLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDictItemLogic {
	return &GetDictItemLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取字典项详情
func (l *GetDictItemLogic) GetDictItem(in *dict_item.GetDictItemReq) (*dict_item.GetDictItemResp, error) {
	// todo: add your logic here and delete this line

	return &dict_item.GetDictItemResp{}, nil
}
