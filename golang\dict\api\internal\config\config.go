package config

import (
	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/zrpc"
)

type Config struct {
	rest.RestConf

	// dict_rpc 配置
	DictRpc zrpc.RpcClientConf `json:"dict_rpc"`

	// dict_category_rpc 配置
	DictCategoryRpc zrpc.RpcClientConf `json:"dictcategory_rpc"`

	// dict_item_rpc 配置
	DictItemRpc zrpc.RpcClientConf `json:"dictitem_rpc"`

	Auth struct {
		AccessSecret string `json:"access_secret"`
		AccessExpire int64  `json:"access_expire"`
	}
}
