syntax = "proto3";

package dict;

option go_package = "./dict";

// 字典基本信息
message Dict {
  int64 id = 1;
  string code = 2;
  string name = 3;
  string remark = 4;
  int32 status = 5;
  string created_time = 6;
  string updated_time = 7;
}

// 创建字典请求
message CreateDictReq {
  string code = 1;
  string name = 2;
  string remark = 3;
  int32 status = 4;
}

// 创建字典响应
message CreateDictResp {
  int64 id = 1;
  string message = 2;
}

// 更新字典请求
message UpdateDictReq {
  int64 id = 1;
  string code = 2;
  string name = 3;
  string remark = 4;
  int32 status = 5;
}

// 更新字典响应
message UpdateDictResp {
  bool success = 1;
  string message = 2;
}

// 删除字典请求
message DeleteDictReq {
  int64 id = 1;
}

// 删除字典响应
message DeleteDictResp {
  bool success = 1;
  string message = 2;
}

// 获取字典详情请求
message GetDictReq {
  int64 id = 1;
}

// 获取字典详情响应
message GetDictResp {
  Dict dict = 1;
  string message = 2;
}

// 字典列表请求
message ListDictReq {
  int32 page = 1;
  int32 page_size = 2;
  string code = 3; // 模糊查询
  string name = 4; // 模糊查询
  int32 status = 5; // -1:全部, 0:禁用, 1:启用
}

// 字典列表响应
message ListDictResp {
  int64 total = 1;
  repeated Dict list = 2;
  string message = 3;
}

// 字典服务
service DictService {
  // 创建字典
  rpc CreateDict(CreateDictReq) returns(CreateDictResp);
  // 更新字典
  rpc UpdateDict(UpdateDictReq) returns(UpdateDictResp);
  // 删除字典
  rpc DeleteDict(DeleteDictReq) returns(DeleteDictResp);
  // 获取字典详情
  rpc GetDict(GetDictReq) returns(GetDictResp);
  // 字典列表
  rpc ListDict(ListDictReq) returns(ListDictResp);
} 