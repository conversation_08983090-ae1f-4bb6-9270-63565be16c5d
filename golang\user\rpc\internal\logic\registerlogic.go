package logic

import (
	"context"
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"
	"user/rpc/internal/svc"

	"user/rpc/model"
	"user/rpc/user"
)

type RegisterLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewRegisterLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RegisterLogic {
	return &RegisterLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// Register creates a new user
func (l *RegisterLogic) Register(in *user.RegisterRequest) (*user.UserResponse, error) {
	// 在注册之前先检查该用户是否已经注册过
	existUser, err := l.svcCtx.UserModel.FindByUsername(in.Username)
	if err == nil && existUser != nil {
		return nil, fmt.Errorf("用户名已存在，请登录")
	}

	// 用户名不存在开始进行注册用户
	err = l.svcCtx.UserModel.CreateUser(&model.User{
		Username: in.Username,
		Password: in.Password,
		Email:    in.Email,
		Phone:    in.Phone,
	}, in.Username)

	if err != nil {
		return nil, fmt.Errorf("注册失败，请重新注册: %v", err)
	}

	// 查询数据库获取最新的记录
	existUser, err = l.svcCtx.UserModel.FindByUsername(in.Username)
	if err != nil {
		return nil, fmt.Errorf("查询用户失败: %v", err)
	}

	// 返回数据给api服务
	return &user.UserResponse{
		Code:    200,
		Message: "注册成功",
		Id:      existUser.ID,
		Username: existUser.Username,
		Email:    existUser.Email,
		Phone:    existUser.Phone,
	}, nil
}
