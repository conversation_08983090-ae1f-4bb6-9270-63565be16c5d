package logic

import (
	"context"

	"dict_category_rpc/dict_category"
	"dict_category_rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDictCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewDeleteDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDictCategoryLogic {
	return &DeleteDictCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 删除字典分类
func (l *DeleteDictCategoryLogic) DeleteDictCategory(in *dict_category.DeleteDictCategoryReq) (*dict_category.DeleteDictCategoryResp, error) {
	// todo: add your logic here and delete this line

	return &dict_category.DeleteDictCategoryResp{}, nil
}
