package logic

import (
	"context"

	"dict_category_rpc/dict_category"
	"dict_category_rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateDictCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDictCategoryLogic {
	return &UpdateDictCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 更新字典分类
func (l *UpdateDictCategoryLogic) UpdateDictCategory(in *dict_category.UpdateDictCategoryReq) (*dict_category.UpdateDictCategoryResp, error) {
	// todo: add your logic here and delete this line

	return &dict_category.UpdateDictCategoryResp{}, nil
}
