package logic

import (
	"context"
	"fmt"

	"user/api/internal/middleware/JWT"
	"user/api/internal/svc"
	"user/api/internal/types"
	"user/rpc/user"

	"github.com/zeromicro/go-zero/core/logx"
)

type LoginLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewLoginLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LoginLogic {
	return &LoginLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *LoginLogic) Login(req *types.LoginRequest) (resp *types.UserResponse, err error) {

	// 调用 rpc 服务的Login方法
	rpcResp, err := l.svcCtx.UserRpc.Login(l.ctx, &user.LoginRequest{
		Username: req.Username,
		Password: req.Password,
	})

	if err != nil {
		return nil, fmt.Errorf("调用rpc服务的Login方法失败")
	}

	// 生成 token 返回给前端页面
	token, err := JWT.GetJwtToken(l.svcCtx.Config.Auth.AccessSecret, 0, l.svcCtx.Config.Auth.AccessExpire, rpcResp.Username)

	if err != nil {
		return nil, fmt.Errorf("生成token失败: %v", err)
	}

	// 返回数据给前端页面
	return &types.UserResponse{
		Code:    int(rpcResp.Code),
		Message: rpcResp.Message,
		BaseResponse: types.BaseResponse{
			Id:       rpcResp.Id,
			Username: rpcResp.Username,
			Email:    rpcResp.Email,
			Phone:    rpcResp.Phone,
			Token:    token,
		},
	}, nil
}
