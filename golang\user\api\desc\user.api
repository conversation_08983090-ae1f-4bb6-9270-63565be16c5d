syntax = "v1"

info (
	title:   "User Service API"
	desc:    "User service for authentication and user management"
	author:  "System Admin"
	email:   "<EMAIL>"
	version: "1.0"
)

type (
	RegisterRequest {
		Username string `json:"username" validate:"required"`
		Password string `json:"password" validate:"required"`
		Email    string `json:"email" validate:"required,email"`
		Phone    string `json:"phone,optional"`
	}
	LoginRequest {
		Username string `json:"username" validate:"required"`
		Password string `json:"password" validate:"required"`
	}
	UserResponse {
		BaseResponse BaseResponse `json:"base_response"`
		Code         int          `json:"code"`
		Message      string       `json:"message"`
	}
	BaseResponse {
		Id       int64  `json:"id"`
		Username string `json:"username"`
		Email    string `json:"email"`
		Phone    string `json:"phone,optional"`
		Token    string `json:"token"`
	}
)

service User {
	@handler Register
	post /api/user/register (RegisterRequest) returns (UserResponse)

	@handler Login
	post /api/user/login (LoginRequest) returns (UserResponse)
}

