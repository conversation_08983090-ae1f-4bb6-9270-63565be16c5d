syntax = "proto3";

package user;

option go_package = "./user";


service User {
  
  rpc Register(RegisterRequest) returns (UserResponse) {}
  
  
  rpc Login(LoginRequest) returns (UserResponse) {}
  
}


message RegisterRequest {
  string username = 1;
  string password = 2;
  string email = 3;
  string phone = 4;
}


message LoginRequest {
  string username = 1;
  string password = 2;
}


message GetUserRequest {
  int64 id = 1;
}


message UserResponse {
  int64 id = 1;
  string username = 2;
  string email = 3;
  string phone = 4;
  int32 code = 6;
  string message = 7;
} 