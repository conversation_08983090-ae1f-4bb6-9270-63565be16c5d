// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5
// Source: dict_item.proto

package server

import (
	"context"

	"dict_item_rpc/dict_item"
	"dict_item_rpc/internal/logic"
	"dict_item_rpc/internal/svc"
)

type DictItemServiceServer struct {
	svcCtx *svc.ServiceContext
	dict_item.UnimplementedDictItemServiceServer
}

func NewDictItemServiceServer(svcCtx *svc.ServiceContext) *DictItemServiceServer {
	return &DictItemServiceServer{
		svcCtx: svcCtx,
	}
}

// 创建字典项
func (s *DictItemServiceServer) CreateDictItem(ctx context.Context, in *dict_item.CreateDictItemReq) (*dict_item.CreateDictItemResp, error) {
	l := logic.NewCreateDictItemLogic(ctx, s.svcCtx)
	return l.CreateDictItem(in)
}

// 更新字典项
func (s *DictItemServiceServer) UpdateDictItem(ctx context.Context, in *dict_item.UpdateDictItemReq) (*dict_item.UpdateDictItemResp, error) {
	l := logic.NewUpdateDictItemLogic(ctx, s.svcCtx)
	return l.UpdateDictItem(in)
}

// 删除字典项
func (s *DictItemServiceServer) DeleteDictItem(ctx context.Context, in *dict_item.DeleteDictItemReq) (*dict_item.DeleteDictItemResp, error) {
	l := logic.NewDeleteDictItemLogic(ctx, s.svcCtx)
	return l.DeleteDictItem(in)
}

// 获取字典项详情
func (s *DictItemServiceServer) GetDictItem(ctx context.Context, in *dict_item.GetDictItemReq) (*dict_item.GetDictItemResp, error) {
	l := logic.NewGetDictItemLogic(ctx, s.svcCtx)
	return l.GetDictItem(in)
}

// 字典项列表
func (s *DictItemServiceServer) ListDictItem(ctx context.Context, in *dict_item.ListDictItemReq) (*dict_item.ListDictItemResp, error) {
	l := logic.NewListDictItemLogic(ctx, s.svcCtx)
	return l.ListDictItem(in)
}
