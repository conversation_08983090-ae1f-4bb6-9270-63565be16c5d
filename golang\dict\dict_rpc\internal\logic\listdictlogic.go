package logic

import (
	"context"
	"fmt"

	"dict_rpc/dict"
	"dict_rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListDictLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewListDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListDictLogic {
	return &ListDictLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 字典列表
func (l *ListDictLogic) ListDict(in *dict.ListDictReq) (*dict.ListDictResp, error) {
	// todo: add your logic here and delete this line

	// 设置默认分页参数
	if in.Page <= 0 {
		in.Page = 1
	}
	if in.PageSize <= 0 {
		in.PageSize = 10
	}
	// 查询字典列表
	DataList,total,err:=l.svcCtx.DictModel.List(in.Page, in.PageSize)

	if err != nil {
		return nil, fmt.Errorf("查询字典列表失败: %v", err)
	}
	var dataList []*dict.Dict
	for _, v := range DataList {
		dataList = append(dataList, &dict.Dict{
			Id:          v.ID,
			Code:        v.Code,
			Name:        v.Name,
			Remark:      v.Remark,
			Status:      v.Status,
			CreatedTime: v.CreatedTime.Format("2006-01-02 15:04:05"),
			UpdatedTime: v.UpdatedTime.Format("2006-01-02 15:04:05"),
		})
	}

	return &dict.ListDictResp{
		Total: total,
		List:  dataList,
		Message: "获取字典列表成功",
	}, nil
}
