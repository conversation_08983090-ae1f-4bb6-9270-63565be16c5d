package logic

import (
	"context"
	"fmt"
	"user/api/internal/middleware/JWT"
	"user/api/internal/svc"
	"user/api/internal/types"
	"user/rpc/user"

	"github.com/zeromicro/go-zero/core/logx"
)

type RegisterLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewRegisterLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RegisterLogic {
	return &RegisterLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *RegisterLogic) Register(req *types.RegisterRequest) (resp *types.UserResponse, err error) {

	// 向rpc发送请求开始注册
	rpcResp, err := l.svcCtx.UserRpc.Register(l.ctx, &user.RegisterRequest{
		Username: req.Username,
		Password: req.Password,
		Email:    req.Email,
		Phone:    req.Phone,
	})

	if err != nil {
		return nil, fmt.Errorf("调用 RPC Register失败: %v", err)
	}

	// 生成 token 返回给前端页面
	token, err := JWT.GetJwtToken(l.svcCtx.Config.Auth.AccessSecret, 0, l.svcCtx.Config.Auth.AccessExpire, rpcResp.Username)

	if err != nil {
		return nil, fmt.Errorf("生成token失败: %v", err)
	}

	// 返回数据给前端页面
	return &types.UserResponse{
		Code:    int(rpcResp.Code),
		Message: "注册成功",
		BaseResponse: types.BaseResponse{
			Id:       rpcResp.Id,
			Username: rpcResp.Username,
			Email:    rpcResp.Email,
			Phone:    rpcResp.Phone,
			Token:    token,
		},
	}, nil
}
