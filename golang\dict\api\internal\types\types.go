// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5

package types

type CreateDictCategoryReq struct {
	DictId int64  `json:"dict_id"`
	Name   string `json:"name"`
	Status int32  `json:"status"`
}

type CreateDictCategoryResp struct {
	Id      int64  `json:"id"`
	Message string `json:"message"`
}

type CreateDictItemReq struct {
	DictId     int64  `json:"dict_id"`
	CategoryId int64  `json:"category_id"`
	Code       string `json:"code"`
	Name       string `json:"name"`
	Status     int32  `json:"status"`
}

type CreateDictItemResp struct {
	Id      int64  `json:"id"`
	Message string `json:"message"`
}

type CreateDictReq struct {
	Code   string `json:"code"`
	Name   string `json:"name"`
	Remark string `json:"remark"`
	Status int32  `json:"status"`
}

type CreateDictResp struct {
	Id      int64  `json:"id"`
	Message string `json:"message"`
}

type DeleteDictCategoryReq struct {
	Id int64 `path:"id"`
}

type DeleteDictCategoryResp struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type DeleteDictItemReq struct {
	Id int64 `path:"id"`
}

type DeleteDictItemResp struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type DeleteDictReq struct {
	Id int64 `path:"id"`
}

type DeleteDictResp struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type Dict struct {
	Id          int64  `json:"id"`
	Code        string `json:"code"`
	Name        string `json:"name"`
	Remark      string `json:"remark"`
	Status      int32  `json:"status"`
	CreatedTime string `json:"created_time"`
	UpdatedTime string `json:"updated_time"`
}

type DictCategory struct {
	Id          int64  `json:"id"`
	DictId      int64  `json:"dict_id"`
	Name        string `json:"name"`
	Status      int32  `json:"status"`
	CreatedTime string `json:"created_time"`
	UpdatedTime string `json:"updated_time"`
}

type DictItem struct {
	Id          int64  `json:"id"`
	DictId      int64  `json:"dict_id"`
	CategoryId  int64  `json:"category_id"`
	Code        string `json:"code"`
	Name        string `json:"name"`
	Status      int32  `json:"status"`
	CreatedTime string `json:"created_time"`
	UpdatedTime string `json:"updated_time"`
}

type GetDictCategoryReq struct {
	Id int64 `path:"id"`
}

type GetDictCategoryResp struct {
	Category DictCategory `json:"category"`
	Message  string       `json:"message"`
}

type GetDictItemReq struct {
	Id int64 `path:"id"`
}

type GetDictItemResp struct {
	Item    DictItem `json:"item"`
	Message string   `json:"message"`
}

type GetDictReq struct {
	Id int64 `path:"id"`
}

type GetDictResp struct {
	Dict    Dict   `json:"dict"`
	Message string `json:"message"`
}

type ListDictCategoryReq struct {
	Page     int32  `form:"page,default=1"`
	PageSize int32  `form:"page_size,default=10"`
	Name     string `form:"name,optional"`
	Status   int32  `form:"status,default=-1"`
	DictId   int64  `form:"dict_id,optional"`
}

type ListDictCategoryResp struct {
	Total   int64          `json:"total"`
	List    []DictCategory `json:"list"`
	Message string         `json:"message"`
}

type ListDictItemReq struct {
	Page       int32  `form:"page,default=1"`
	PageSize   int32  `form:"page_size,default=10"`
	DictId     int64  `form:"dict_id,optional"`
	CategoryId int64  `form:"category_id,optional"`
	Code       string `form:"code,optional"`
	Name       string `form:"name,optional"`
	Status     int32  `form:"status,default=-1"`
}

type ListDictItemResp struct {
	Total   int64      `json:"total"`
	List    []DictItem `json:"list"`
	Message string     `json:"message"`
}

type ListDictReq struct {
	Page     int32  `form:"page,default=1"`
	PageSize int32  `form:"page_size,default=10"`
	Code     string `form:"code,optional"`
	Name     string `form:"name,optional"`
	Status   int32  `form:"status,default=-1"`
}

type ListDictResp struct {
	Total   int64  `json:"total"`
	List    []Dict `json:"list"`
	Message string `json:"message"`
}

type UpdateDictCategoryReq struct {
	Id     int64  `path:"id"`
	DictId int64  `json:"dict_id"`
	Name   string `json:"name"`
	Status int32  `json:"status"`
}

type UpdateDictCategoryResp struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type UpdateDictItemReq struct {
	Id         int64  `path:"id"`
	DictId     int64  `json:"dict_id"`
	CategoryId int64  `json:"category_id"`
	Code       string `json:"code"`
	Name       string `json:"name"`
	Status     int32  `json:"status"`
}

type UpdateDictItemResp struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type UpdateDictReq struct {
	Id     int64  `path:"id"`
	Code   string `json:"code"`
	Name   string `json:"name"`
	Remark string `json:"remark"`
	Status int32  `json:"status"`
}

type UpdateDictResp struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}
