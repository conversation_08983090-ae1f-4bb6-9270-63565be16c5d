package logic

import (
	"context"

	"dict_category_rpc/dict_category"
	"dict_category_rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListDictCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewListDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListDictCategoryLogic {
	return &ListDictCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 字典分类列表
func (l *ListDictCategoryLogic) ListDictCategory(in *dict_category.ListDictCategoryReq) (*dict_category.ListDictCategoryResp, error) {
	// todo: add your logic here and delete this line

	return &dict_category.ListDictCategoryResp{}, nil
}
