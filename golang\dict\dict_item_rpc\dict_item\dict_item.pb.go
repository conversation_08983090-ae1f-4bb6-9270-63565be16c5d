// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.0
// source: dict_item.proto

package dict_item

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 字典项信息
type DictItem struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DictId        int64                  `protobuf:"varint,2,opt,name=dict_id,json=dictId,proto3" json:"dict_id,omitempty"`
	CategoryId    int64                  `protobuf:"varint,3,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	Code          string                 `protobuf:"bytes,4,opt,name=code,proto3" json:"code,omitempty"`
	Name          string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Status        int32                  `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`
	CreatedTime   string                 `protobuf:"bytes,7,opt,name=created_time,json=createdTime,proto3" json:"created_time,omitempty"`
	UpdatedTime   string                 `protobuf:"bytes,8,opt,name=updated_time,json=updatedTime,proto3" json:"updated_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DictItem) Reset() {
	*x = DictItem{}
	mi := &file_dict_item_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DictItem) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DictItem) ProtoMessage() {}

func (x *DictItem) ProtoReflect() protoreflect.Message {
	mi := &file_dict_item_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DictItem.ProtoReflect.Descriptor instead.
func (*DictItem) Descriptor() ([]byte, []int) {
	return file_dict_item_proto_rawDescGZIP(), []int{0}
}

func (x *DictItem) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *DictItem) GetDictId() int64 {
	if x != nil {
		return x.DictId
	}
	return 0
}

func (x *DictItem) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *DictItem) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *DictItem) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DictItem) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *DictItem) GetCreatedTime() string {
	if x != nil {
		return x.CreatedTime
	}
	return ""
}

func (x *DictItem) GetUpdatedTime() string {
	if x != nil {
		return x.UpdatedTime
	}
	return ""
}

// 创建字典项请求
type CreateDictItemReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DictId        int64                  `protobuf:"varint,1,opt,name=dict_id,json=dictId,proto3" json:"dict_id,omitempty"`
	CategoryId    int64                  `protobuf:"varint,2,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	Code          string                 `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Status        int32                  `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDictItemReq) Reset() {
	*x = CreateDictItemReq{}
	mi := &file_dict_item_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDictItemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDictItemReq) ProtoMessage() {}

func (x *CreateDictItemReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_item_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDictItemReq.ProtoReflect.Descriptor instead.
func (*CreateDictItemReq) Descriptor() ([]byte, []int) {
	return file_dict_item_proto_rawDescGZIP(), []int{1}
}

func (x *CreateDictItemReq) GetDictId() int64 {
	if x != nil {
		return x.DictId
	}
	return 0
}

func (x *CreateDictItemReq) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *CreateDictItemReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *CreateDictItemReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateDictItemReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// 创建字典项响应
type CreateDictItemResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDictItemResp) Reset() {
	*x = CreateDictItemResp{}
	mi := &file_dict_item_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDictItemResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDictItemResp) ProtoMessage() {}

func (x *CreateDictItemResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_item_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDictItemResp.ProtoReflect.Descriptor instead.
func (*CreateDictItemResp) Descriptor() ([]byte, []int) {
	return file_dict_item_proto_rawDescGZIP(), []int{2}
}

func (x *CreateDictItemResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CreateDictItemResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 更新字典项请求
type UpdateDictItemReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	DictId        int64                  `protobuf:"varint,2,opt,name=dict_id,json=dictId,proto3" json:"dict_id,omitempty"`
	CategoryId    int64                  `protobuf:"varint,3,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"`
	Code          string                 `protobuf:"bytes,4,opt,name=code,proto3" json:"code,omitempty"`
	Name          string                 `protobuf:"bytes,5,opt,name=name,proto3" json:"name,omitempty"`
	Status        int32                  `protobuf:"varint,6,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDictItemReq) Reset() {
	*x = UpdateDictItemReq{}
	mi := &file_dict_item_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDictItemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDictItemReq) ProtoMessage() {}

func (x *UpdateDictItemReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_item_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDictItemReq.ProtoReflect.Descriptor instead.
func (*UpdateDictItemReq) Descriptor() ([]byte, []int) {
	return file_dict_item_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateDictItemReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateDictItemReq) GetDictId() int64 {
	if x != nil {
		return x.DictId
	}
	return 0
}

func (x *UpdateDictItemReq) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *UpdateDictItemReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *UpdateDictItemReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateDictItemReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// 更新字典项响应
type UpdateDictItemResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDictItemResp) Reset() {
	*x = UpdateDictItemResp{}
	mi := &file_dict_item_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDictItemResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDictItemResp) ProtoMessage() {}

func (x *UpdateDictItemResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_item_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDictItemResp.ProtoReflect.Descriptor instead.
func (*UpdateDictItemResp) Descriptor() ([]byte, []int) {
	return file_dict_item_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateDictItemResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateDictItemResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 删除字典项请求
type DeleteDictItemReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDictItemReq) Reset() {
	*x = DeleteDictItemReq{}
	mi := &file_dict_item_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDictItemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDictItemReq) ProtoMessage() {}

func (x *DeleteDictItemReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_item_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDictItemReq.ProtoReflect.Descriptor instead.
func (*DeleteDictItemReq) Descriptor() ([]byte, []int) {
	return file_dict_item_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteDictItemReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 删除字典项响应
type DeleteDictItemResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDictItemResp) Reset() {
	*x = DeleteDictItemResp{}
	mi := &file_dict_item_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDictItemResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDictItemResp) ProtoMessage() {}

func (x *DeleteDictItemResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_item_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDictItemResp.ProtoReflect.Descriptor instead.
func (*DeleteDictItemResp) Descriptor() ([]byte, []int) {
	return file_dict_item_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteDictItemResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteDictItemResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 获取字典项详情请求
type GetDictItemReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDictItemReq) Reset() {
	*x = GetDictItemReq{}
	mi := &file_dict_item_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDictItemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDictItemReq) ProtoMessage() {}

func (x *GetDictItemReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_item_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDictItemReq.ProtoReflect.Descriptor instead.
func (*GetDictItemReq) Descriptor() ([]byte, []int) {
	return file_dict_item_proto_rawDescGZIP(), []int{7}
}

func (x *GetDictItemReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 获取字典项详情响应
type GetDictItemResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Item          *DictItem              `protobuf:"bytes,1,opt,name=item,proto3" json:"item,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDictItemResp) Reset() {
	*x = GetDictItemResp{}
	mi := &file_dict_item_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDictItemResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDictItemResp) ProtoMessage() {}

func (x *GetDictItemResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_item_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDictItemResp.ProtoReflect.Descriptor instead.
func (*GetDictItemResp) Descriptor() ([]byte, []int) {
	return file_dict_item_proto_rawDescGZIP(), []int{8}
}

func (x *GetDictItemResp) GetItem() *DictItem {
	if x != nil {
		return x.Item
	}
	return nil
}

func (x *GetDictItemResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 字典项列表请求
type ListDictItemReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	DictId        int64                  `protobuf:"varint,3,opt,name=dict_id,json=dictId,proto3" json:"dict_id,omitempty"`             // 指定字典ID
	CategoryId    int64                  `protobuf:"varint,4,opt,name=category_id,json=categoryId,proto3" json:"category_id,omitempty"` // 指定分类ID
	Code          string                 `protobuf:"bytes,5,opt,name=code,proto3" json:"code,omitempty"`                                // 模糊查询
	Name          string                 `protobuf:"bytes,6,opt,name=name,proto3" json:"name,omitempty"`                                // 模糊查询
	Status        int32                  `protobuf:"varint,7,opt,name=status,proto3" json:"status,omitempty"`                           // -1:全部, 0:禁用, 1:启用
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDictItemReq) Reset() {
	*x = ListDictItemReq{}
	mi := &file_dict_item_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDictItemReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDictItemReq) ProtoMessage() {}

func (x *ListDictItemReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_item_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDictItemReq.ProtoReflect.Descriptor instead.
func (*ListDictItemReq) Descriptor() ([]byte, []int) {
	return file_dict_item_proto_rawDescGZIP(), []int{9}
}

func (x *ListDictItemReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListDictItemReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListDictItemReq) GetDictId() int64 {
	if x != nil {
		return x.DictId
	}
	return 0
}

func (x *ListDictItemReq) GetCategoryId() int64 {
	if x != nil {
		return x.CategoryId
	}
	return 0
}

func (x *ListDictItemReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ListDictItemReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListDictItemReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// 字典项列表响应
type ListDictItemResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         int64                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*DictItem            `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDictItemResp) Reset() {
	*x = ListDictItemResp{}
	mi := &file_dict_item_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDictItemResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDictItemResp) ProtoMessage() {}

func (x *ListDictItemResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_item_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDictItemResp.ProtoReflect.Descriptor instead.
func (*ListDictItemResp) Descriptor() ([]byte, []int) {
	return file_dict_item_proto_rawDescGZIP(), []int{10}
}

func (x *ListDictItemResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListDictItemResp) GetList() []*DictItem {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListDictItemResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_dict_item_proto protoreflect.FileDescriptor

const file_dict_item_proto_rawDesc = "" +
	"\n" +
	"\x0fdict_item.proto\x12\tdict_item\"\xda\x01\n" +
	"\bDictItem\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\adict_id\x18\x02 \x01(\x03R\x06dictId\x12\x1f\n" +
	"\vcategory_id\x18\x03 \x01(\x03R\n" +
	"categoryId\x12\x12\n" +
	"\x04code\x18\x04 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x05 \x01(\tR\x04name\x12\x16\n" +
	"\x06status\x18\x06 \x01(\x05R\x06status\x12!\n" +
	"\fcreated_time\x18\a \x01(\tR\vcreatedTime\x12!\n" +
	"\fupdated_time\x18\b \x01(\tR\vupdatedTime\"\x8d\x01\n" +
	"\x11CreateDictItemReq\x12\x17\n" +
	"\adict_id\x18\x01 \x01(\x03R\x06dictId\x12\x1f\n" +
	"\vcategory_id\x18\x02 \x01(\x03R\n" +
	"categoryId\x12\x12\n" +
	"\x04code\x18\x03 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x16\n" +
	"\x06status\x18\x05 \x01(\x05R\x06status\">\n" +
	"\x12CreateDictItemResp\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\x9d\x01\n" +
	"\x11UpdateDictItemReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x17\n" +
	"\adict_id\x18\x02 \x01(\x03R\x06dictId\x12\x1f\n" +
	"\vcategory_id\x18\x03 \x01(\x03R\n" +
	"categoryId\x12\x12\n" +
	"\x04code\x18\x04 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x05 \x01(\tR\x04name\x12\x16\n" +
	"\x06status\x18\x06 \x01(\x05R\x06status\"H\n" +
	"\x12UpdateDictItemResp\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"#\n" +
	"\x11DeleteDictItemReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"H\n" +
	"\x12DeleteDictItemResp\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\" \n" +
	"\x0eGetDictItemReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"T\n" +
	"\x0fGetDictItemResp\x12'\n" +
	"\x04item\x18\x01 \x01(\v2\x13.dict_item.DictItemR\x04item\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\xbc\x01\n" +
	"\x0fListDictItemReq\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x17\n" +
	"\adict_id\x18\x03 \x01(\x03R\x06dictId\x12\x1f\n" +
	"\vcategory_id\x18\x04 \x01(\x03R\n" +
	"categoryId\x12\x12\n" +
	"\x04code\x18\x05 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x06 \x01(\tR\x04name\x12\x16\n" +
	"\x06status\x18\a \x01(\x05R\x06status\"k\n" +
	"\x10ListDictItemResp\x12\x14\n" +
	"\x05total\x18\x01 \x01(\x03R\x05total\x12'\n" +
	"\x04list\x18\x02 \x03(\v2\x13.dict_item.DictItemR\x04list\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage2\x8d\x03\n" +
	"\x0fDictItemService\x12M\n" +
	"\x0eCreateDictItem\x12\x1c.dict_item.CreateDictItemReq\x1a\x1d.dict_item.CreateDictItemResp\x12M\n" +
	"\x0eUpdateDictItem\x12\x1c.dict_item.UpdateDictItemReq\x1a\x1d.dict_item.UpdateDictItemResp\x12M\n" +
	"\x0eDeleteDictItem\x12\x1c.dict_item.DeleteDictItemReq\x1a\x1d.dict_item.DeleteDictItemResp\x12D\n" +
	"\vGetDictItem\x12\x19.dict_item.GetDictItemReq\x1a\x1a.dict_item.GetDictItemResp\x12G\n" +
	"\fListDictItem\x12\x1a.dict_item.ListDictItemReq\x1a\x1b.dict_item.ListDictItemRespB\rZ\v./dict_itemb\x06proto3"

var (
	file_dict_item_proto_rawDescOnce sync.Once
	file_dict_item_proto_rawDescData []byte
)

func file_dict_item_proto_rawDescGZIP() []byte {
	file_dict_item_proto_rawDescOnce.Do(func() {
		file_dict_item_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_dict_item_proto_rawDesc), len(file_dict_item_proto_rawDesc)))
	})
	return file_dict_item_proto_rawDescData
}

var file_dict_item_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_dict_item_proto_goTypes = []any{
	(*DictItem)(nil),           // 0: dict_item.DictItem
	(*CreateDictItemReq)(nil),  // 1: dict_item.CreateDictItemReq
	(*CreateDictItemResp)(nil), // 2: dict_item.CreateDictItemResp
	(*UpdateDictItemReq)(nil),  // 3: dict_item.UpdateDictItemReq
	(*UpdateDictItemResp)(nil), // 4: dict_item.UpdateDictItemResp
	(*DeleteDictItemReq)(nil),  // 5: dict_item.DeleteDictItemReq
	(*DeleteDictItemResp)(nil), // 6: dict_item.DeleteDictItemResp
	(*GetDictItemReq)(nil),     // 7: dict_item.GetDictItemReq
	(*GetDictItemResp)(nil),    // 8: dict_item.GetDictItemResp
	(*ListDictItemReq)(nil),    // 9: dict_item.ListDictItemReq
	(*ListDictItemResp)(nil),   // 10: dict_item.ListDictItemResp
}
var file_dict_item_proto_depIdxs = []int32{
	0,  // 0: dict_item.GetDictItemResp.item:type_name -> dict_item.DictItem
	0,  // 1: dict_item.ListDictItemResp.list:type_name -> dict_item.DictItem
	1,  // 2: dict_item.DictItemService.CreateDictItem:input_type -> dict_item.CreateDictItemReq
	3,  // 3: dict_item.DictItemService.UpdateDictItem:input_type -> dict_item.UpdateDictItemReq
	5,  // 4: dict_item.DictItemService.DeleteDictItem:input_type -> dict_item.DeleteDictItemReq
	7,  // 5: dict_item.DictItemService.GetDictItem:input_type -> dict_item.GetDictItemReq
	9,  // 6: dict_item.DictItemService.ListDictItem:input_type -> dict_item.ListDictItemReq
	2,  // 7: dict_item.DictItemService.CreateDictItem:output_type -> dict_item.CreateDictItemResp
	4,  // 8: dict_item.DictItemService.UpdateDictItem:output_type -> dict_item.UpdateDictItemResp
	6,  // 9: dict_item.DictItemService.DeleteDictItem:output_type -> dict_item.DeleteDictItemResp
	8,  // 10: dict_item.DictItemService.GetDictItem:output_type -> dict_item.GetDictItemResp
	10, // 11: dict_item.DictItemService.ListDictItem:output_type -> dict_item.ListDictItemResp
	7,  // [7:12] is the sub-list for method output_type
	2,  // [2:7] is the sub-list for method input_type
	2,  // [2:2] is the sub-list for extension type_name
	2,  // [2:2] is the sub-list for extension extendee
	0,  // [0:2] is the sub-list for field type_name
}

func init() { file_dict_item_proto_init() }
func file_dict_item_proto_init() {
	if File_dict_item_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_dict_item_proto_rawDesc), len(file_dict_item_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_dict_item_proto_goTypes,
		DependencyIndexes: file_dict_item_proto_depIdxs,
		MessageInfos:      file_dict_item_proto_msgTypes,
	}.Build()
	File_dict_item_proto = out.File
	file_dict_item_proto_goTypes = nil
	file_dict_item_proto_depIdxs = nil
}
