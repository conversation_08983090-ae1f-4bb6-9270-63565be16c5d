// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5
// Source: user.proto

package server

import (
	"context"

	"user/rpc/internal/logic"
	"user/rpc/internal/svc"
	"user/rpc/user"
)

type UserServer struct {
	svcCtx *svc.ServiceContext
	user.UnimplementedUserServer
}

func NewUserServer(svcCtx *svc.ServiceContext) *UserServer {
	return &UserServer{
		svcCtx: svcCtx,
	}
}

func (s *UserServer) Register(ctx context.Context, in *user.RegisterRequest) (*user.UserResponse, error) {
	l := logic.NewRegisterLogic(ctx, s.svcCtx)
	return l.Register(in)
}

func (s *UserServer) Login(ctx context.Context, in *user.LoginRequest) (*user.UserResponse, error) {
	l := logic.NewLoginLogic(ctx, s.svcCtx)
	return l.<PERSON><PERSON>(in)
}
