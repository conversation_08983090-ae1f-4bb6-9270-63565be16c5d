// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v6.31.0
// source: dict.proto

package dict

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 字典基本信息
type Dict struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Code          string                 `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Remark        string                 `protobuf:"bytes,4,opt,name=remark,proto3" json:"remark,omitempty"`
	Status        int32                  `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	CreatedTime   string                 `protobuf:"bytes,6,opt,name=created_time,json=createdTime,proto3" json:"created_time,omitempty"`
	UpdatedTime   string                 `protobuf:"bytes,7,opt,name=updated_time,json=updatedTime,proto3" json:"updated_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Dict) Reset() {
	*x = Dict{}
	mi := &file_dict_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Dict) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Dict) ProtoMessage() {}

func (x *Dict) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Dict.ProtoReflect.Descriptor instead.
func (*Dict) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{0}
}

func (x *Dict) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Dict) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *Dict) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Dict) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *Dict) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

func (x *Dict) GetCreatedTime() string {
	if x != nil {
		return x.CreatedTime
	}
	return ""
}

func (x *Dict) GetUpdatedTime() string {
	if x != nil {
		return x.UpdatedTime
	}
	return ""
}

// 创建字典请求
type CreateDictReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          string                 `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Remark        string                 `protobuf:"bytes,3,opt,name=remark,proto3" json:"remark,omitempty"`
	Status        int32                  `protobuf:"varint,4,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDictReq) Reset() {
	*x = CreateDictReq{}
	mi := &file_dict_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDictReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDictReq) ProtoMessage() {}

func (x *CreateDictReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDictReq.ProtoReflect.Descriptor instead.
func (*CreateDictReq) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{1}
}

func (x *CreateDictReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *CreateDictReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateDictReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *CreateDictReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// 创建字典响应
type CreateDictResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDictResp) Reset() {
	*x = CreateDictResp{}
	mi := &file_dict_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDictResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDictResp) ProtoMessage() {}

func (x *CreateDictResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDictResp.ProtoReflect.Descriptor instead.
func (*CreateDictResp) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{2}
}

func (x *CreateDictResp) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *CreateDictResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 更新字典请求
type UpdateDictReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Code          string                 `protobuf:"bytes,2,opt,name=code,proto3" json:"code,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Remark        string                 `protobuf:"bytes,4,opt,name=remark,proto3" json:"remark,omitempty"`
	Status        int32                  `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDictReq) Reset() {
	*x = UpdateDictReq{}
	mi := &file_dict_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDictReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDictReq) ProtoMessage() {}

func (x *UpdateDictReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDictReq.ProtoReflect.Descriptor instead.
func (*UpdateDictReq) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateDictReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateDictReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *UpdateDictReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateDictReq) GetRemark() string {
	if x != nil {
		return x.Remark
	}
	return ""
}

func (x *UpdateDictReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// 更新字典响应
type UpdateDictResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateDictResp) Reset() {
	*x = UpdateDictResp{}
	mi := &file_dict_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateDictResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDictResp) ProtoMessage() {}

func (x *UpdateDictResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDictResp.ProtoReflect.Descriptor instead.
func (*UpdateDictResp) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateDictResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *UpdateDictResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 删除字典请求
type DeleteDictReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDictReq) Reset() {
	*x = DeleteDictReq{}
	mi := &file_dict_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDictReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDictReq) ProtoMessage() {}

func (x *DeleteDictReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDictReq.ProtoReflect.Descriptor instead.
func (*DeleteDictReq) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteDictReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 删除字典响应
type DeleteDictResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDictResp) Reset() {
	*x = DeleteDictResp{}
	mi := &file_dict_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDictResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDictResp) ProtoMessage() {}

func (x *DeleteDictResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDictResp.ProtoReflect.Descriptor instead.
func (*DeleteDictResp) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteDictResp) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *DeleteDictResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 获取字典详情请求
type GetDictReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDictReq) Reset() {
	*x = GetDictReq{}
	mi := &file_dict_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDictReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDictReq) ProtoMessage() {}

func (x *GetDictReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDictReq.ProtoReflect.Descriptor instead.
func (*GetDictReq) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{7}
}

func (x *GetDictReq) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 获取字典详情响应
type GetDictResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Dict          *Dict                  `protobuf:"bytes,1,opt,name=dict,proto3" json:"dict,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDictResp) Reset() {
	*x = GetDictResp{}
	mi := &file_dict_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDictResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDictResp) ProtoMessage() {}

func (x *GetDictResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDictResp.ProtoReflect.Descriptor instead.
func (*GetDictResp) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{8}
}

func (x *GetDictResp) GetDict() *Dict {
	if x != nil {
		return x.Dict
	}
	return nil
}

func (x *GetDictResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// 字典列表请求
type ListDictReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	PageSize      int32                  `protobuf:"varint,2,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	Code          string                 `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`      // 模糊查询
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`      // 模糊查询
	Status        int32                  `protobuf:"varint,5,opt,name=status,proto3" json:"status,omitempty"` // -1:全部, 0:禁用, 1:启用
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDictReq) Reset() {
	*x = ListDictReq{}
	mi := &file_dict_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDictReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDictReq) ProtoMessage() {}

func (x *ListDictReq) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDictReq.ProtoReflect.Descriptor instead.
func (*ListDictReq) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{9}
}

func (x *ListDictReq) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListDictReq) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListDictReq) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ListDictReq) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListDictReq) GetStatus() int32 {
	if x != nil {
		return x.Status
	}
	return 0
}

// 字典列表响应
type ListDictResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         int64                  `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*Dict                `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	Message       string                 `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListDictResp) Reset() {
	*x = ListDictResp{}
	mi := &file_dict_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListDictResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDictResp) ProtoMessage() {}

func (x *ListDictResp) ProtoReflect() protoreflect.Message {
	mi := &file_dict_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDictResp.ProtoReflect.Descriptor instead.
func (*ListDictResp) Descriptor() ([]byte, []int) {
	return file_dict_proto_rawDescGZIP(), []int{10}
}

func (x *ListDictResp) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListDictResp) GetList() []*Dict {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *ListDictResp) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

var File_dict_proto protoreflect.FileDescriptor

const file_dict_proto_rawDesc = "" +
	"\n" +
	"\n" +
	"dict.proto\x12\x04dict\"\xb4\x01\n" +
	"\x04Dict\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04code\x18\x02 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x16\n" +
	"\x06remark\x18\x04 \x01(\tR\x06remark\x12\x16\n" +
	"\x06status\x18\x05 \x01(\x05R\x06status\x12!\n" +
	"\fcreated_time\x18\x06 \x01(\tR\vcreatedTime\x12!\n" +
	"\fupdated_time\x18\a \x01(\tR\vupdatedTime\"g\n" +
	"\rCreateDictReq\x12\x12\n" +
	"\x04code\x18\x01 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x16\n" +
	"\x06remark\x18\x03 \x01(\tR\x06remark\x12\x16\n" +
	"\x06status\x18\x04 \x01(\x05R\x06status\":\n" +
	"\x0eCreateDictResp\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"w\n" +
	"\rUpdateDictReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\x12\x12\n" +
	"\x04code\x18\x02 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x16\n" +
	"\x06remark\x18\x04 \x01(\tR\x06remark\x12\x16\n" +
	"\x06status\x18\x05 \x01(\x05R\x06status\"D\n" +
	"\x0eUpdateDictResp\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\x1f\n" +
	"\rDeleteDictReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"D\n" +
	"\x0eDeleteDictResp\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\x1c\n" +
	"\n" +
	"GetDictReq\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x03R\x02id\"G\n" +
	"\vGetDictResp\x12\x1e\n" +
	"\x04dict\x18\x01 \x01(\v2\n" +
	".dict.DictR\x04dict\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"~\n" +
	"\vListDictReq\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1b\n" +
	"\tpage_size\x18\x02 \x01(\x05R\bpageSize\x12\x12\n" +
	"\x04code\x18\x03 \x01(\tR\x04code\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\x12\x16\n" +
	"\x06status\x18\x05 \x01(\x05R\x06status\"^\n" +
	"\fListDictResp\x12\x14\n" +
	"\x05total\x18\x01 \x01(\x03R\x05total\x12\x1e\n" +
	"\x04list\x18\x02 \x03(\v2\n" +
	".dict.DictR\x04list\x12\x18\n" +
	"\amessage\x18\x03 \x01(\tR\amessage2\x9b\x02\n" +
	"\vDictService\x127\n" +
	"\n" +
	"CreateDict\x12\x13.dict.CreateDictReq\x1a\x14.dict.CreateDictResp\x127\n" +
	"\n" +
	"UpdateDict\x12\x13.dict.UpdateDictReq\x1a\x14.dict.UpdateDictResp\x127\n" +
	"\n" +
	"DeleteDict\x12\x13.dict.DeleteDictReq\x1a\x14.dict.DeleteDictResp\x12.\n" +
	"\aGetDict\x12\x10.dict.GetDictReq\x1a\x11.dict.GetDictResp\x121\n" +
	"\bListDict\x12\x11.dict.ListDictReq\x1a\x12.dict.ListDictRespB\bZ\x06./dictb\x06proto3"

var (
	file_dict_proto_rawDescOnce sync.Once
	file_dict_proto_rawDescData []byte
)

func file_dict_proto_rawDescGZIP() []byte {
	file_dict_proto_rawDescOnce.Do(func() {
		file_dict_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_dict_proto_rawDesc), len(file_dict_proto_rawDesc)))
	})
	return file_dict_proto_rawDescData
}

var file_dict_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_dict_proto_goTypes = []any{
	(*Dict)(nil),           // 0: dict.Dict
	(*CreateDictReq)(nil),  // 1: dict.CreateDictReq
	(*CreateDictResp)(nil), // 2: dict.CreateDictResp
	(*UpdateDictReq)(nil),  // 3: dict.UpdateDictReq
	(*UpdateDictResp)(nil), // 4: dict.UpdateDictResp
	(*DeleteDictReq)(nil),  // 5: dict.DeleteDictReq
	(*DeleteDictResp)(nil), // 6: dict.DeleteDictResp
	(*GetDictReq)(nil),     // 7: dict.GetDictReq
	(*GetDictResp)(nil),    // 8: dict.GetDictResp
	(*ListDictReq)(nil),    // 9: dict.ListDictReq
	(*ListDictResp)(nil),   // 10: dict.ListDictResp
}
var file_dict_proto_depIdxs = []int32{
	0,  // 0: dict.GetDictResp.dict:type_name -> dict.Dict
	0,  // 1: dict.ListDictResp.list:type_name -> dict.Dict
	1,  // 2: dict.DictService.CreateDict:input_type -> dict.CreateDictReq
	3,  // 3: dict.DictService.UpdateDict:input_type -> dict.UpdateDictReq
	5,  // 4: dict.DictService.DeleteDict:input_type -> dict.DeleteDictReq
	7,  // 5: dict.DictService.GetDict:input_type -> dict.GetDictReq
	9,  // 6: dict.DictService.ListDict:input_type -> dict.ListDictReq
	2,  // 7: dict.DictService.CreateDict:output_type -> dict.CreateDictResp
	4,  // 8: dict.DictService.UpdateDict:output_type -> dict.UpdateDictResp
	6,  // 9: dict.DictService.DeleteDict:output_type -> dict.DeleteDictResp
	8,  // 10: dict.DictService.GetDict:output_type -> dict.GetDictResp
	10, // 11: dict.DictService.ListDict:output_type -> dict.ListDictResp
	7,  // [7:12] is the sub-list for method output_type
	2,  // [2:7] is the sub-list for method input_type
	2,  // [2:2] is the sub-list for extension type_name
	2,  // [2:2] is the sub-list for extension extendee
	0,  // [0:2] is the sub-list for field type_name
}

func init() { file_dict_proto_init() }
func file_dict_proto_init() {
	if File_dict_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_dict_proto_rawDesc), len(file_dict_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_dict_proto_goTypes,
		DependencyIndexes: file_dict_proto_depIdxs,
		MessageInfos:      file_dict_proto_msgTypes,
	}.Build()
	File_dict_proto = out.File
	file_dict_proto_goTypes = nil
	file_dict_proto_depIdxs = nil
}
