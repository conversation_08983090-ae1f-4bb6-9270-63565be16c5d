package logic

import (
	"context"
	"errors"

	"dict_rpc/dict"
	"dict_rpc/internal/svc"


	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type GetDictLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDictLogic {
	return &GetDictLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取字典详情
func (l *GetDictLogic) GetDict(in *dict.GetDictReq) (*dict.GetDictResp, error) {
	// todo: add your logic here and delete this line
	// 参数验证
	if in.Id <= 0 {
		return &dict.GetDictResp{
			Dict:    nil,
			Message: "字典ID不能为空",
		}, nil
	}

	// 根据id查询字典
	dictModel, err := l.svcCtx.DictModel.FindByID(in.Id)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &dict.GetDictResp{
				Dict:    nil,
				Message: "字典不存在",
			}, nil
		}
		return &dict.GetDictResp{
			Dict:    nil,
			Message: "查询字典失败",
		}, nil
	}



	return &dict.GetDictResp{
		Dict: &dict.Dict{
			Id:          dictModel.ID,
			Code:        dictModel.Code,
			Name:        dictModel.Name,
			Remark:      dictModel.Remark,
			Status:      dictModel.Status,
			CreatedTime: dictModel.CreatedTime.Format("2006-01-02 15:04:05"),
			UpdatedTime: dictModel.UpdatedTime.Format("2006-01-02 15:04:05"),
		},
		Message: "获取字典详情成功",
	}, nil
}
