package model

import (
	"errors"
	"strconv"
	"time"

	"github.com/duke-git/lancet/v2/cryptor"
	"gorm.io/gorm"
)

// User 用户模型
type User struct {
	ID        int64          `gorm:"primaryKey;autoIncrement"`
	Username  string         `gorm:"type:varchar(50);uniqueIndex;not null"`
	Password  string         `gorm:"type:varchar(100);not null"`
	Email     string         `gorm:"type:varchar(100);uniqueIndex;not null"`
	Phone     string         `gorm:"type:varchar(20)"`
	CreatedAt time.Time      `gorm:"not null"`
	UpdatedAt time.Time      `gorm:"not null"`
	DeletedAt gorm.DeletedAt `gorm:"index"`
}

// TableName 设置表名
func (User) TableName() string {
	return "users"
}

// 定义一个用户模型接口 - UserModel
type UserModel struct {
	db *gorm.DB
}

// 创建一个用户模型 - NewUserModel
func NewUserModel(db *gorm.DB) *UserModel {
	return &UserModel{
		db: db,
	}
}

// 给模型绑定方法 ——根据ID查询用户 FindByID
func (m *UserModel) FindByID(id int64) (*User, error) {
	var user User
	result := m.db.First(&user, id)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户id=" + strconv.Itoa(int(id)) + "不存在")
		}
		return nil, result.Error
	}
	return &user, nil
}

// 给模型绑定方法 ——根据用户名查询用户 FindByUsername
func (m *UserModel) FindByUsername(username string) (*User, error) {
	var user User
	result := m.db.Where("username=?", username).First(&user)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户名=" + username + "不存在")
		}
		return nil, result.Error
	}
	return &user, nil
}

// 给模型绑定方法 —— 创建用户 CreateUser
func (m *UserModel) CreateUser(user *User, key string) error {
	//  先对用户的密码进行加密处理
	hashPassword := cryptor.HmacSha256(user.Password, key)
	user.Password = hashPassword

	return m.db.Create(user).Error
}

// 给模型绑定方法 —— 验证密码是否正确
func (m *UserModel) VerifyPassword(user *User, password, key string) bool {
	return user.Password == cryptor.HmacSha256(password, key)
}

// 给模型绑定方法 —— 更新用户信息
func (m *UserModel) UpdateUser(user *User) error {
	return m.db.Save(user).Error
}
