package logic

import (
	"context"
	"fmt"

	"user/rpc/internal/svc"
	"user/rpc/user"

	"github.com/zeromicro/go-zero/core/logx"
)

type LoginLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewLoginLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LoginLogic {
	return &LoginLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// Login authenticates a user and returns user information with token
func (l *LoginLogic) Login(in *user.LoginRequest) (*user.UserResponse, error) {
	// 根据用户名查询用户
	existUser, err := l.svcCtx.UserModel.FindByUsername(in.Username)
	if err != nil {
		return nil, fmt.Errorf("用户不存在: %v", err)
	}

	// 验证密码
	if !l.svcCtx.UserModel.VerifyPassword(existUser, in.Password, l.svcCtx.Config.Auth.AccessSecret) {
		return nil, fmt.Errorf("密码错误")
	}

	return &user.UserResponse{
		Id:       existUser.ID,
		Username: existUser.Username,
		Email:    existUser.Email,
		Phone:    existUser.Phone,
		Code:     200,
		Message:  "登录成功",
	}, nil
}
