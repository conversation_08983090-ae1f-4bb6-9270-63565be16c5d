### Go-Zero Docker-compose 开发环境

| 容器名称     | 暴露端口                                                                        | host 地址    | 说明                                                                                                                                                                                                                                                                 |
| ------------ | ------------------------------------------------------------------------------- | ------------ | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| golang       | 8000:8000 8001:8001 8002:8002 8003:8003 9000:9000 9001:9001 9002:9002 9003:9003 | golang       | 在生产环境中微服务一般都是集群部署，可能一个微服务一台服务器，也可能一个微服务一个容器。为了方便开发调试，我们将在 `golang` 容器中启动所有微服务，并为它们分配监听不同的端口号以示区分。 80：开头的端口号我们将用于 `api` 服务 90：开头的端口号我们将用于 `rpc` 服务 |
| dtm          | 36789:36789 36790:36790                                                         | dtm          | `dtm` 的 `http` 协议和 `grpc` 协议服务端口号，供客户端交互使用。 此项目中我们只在 `Docker` 内部容器之间访问使用，所以也可以不暴露端口号给宿主机                                                                                                                      |
| etcd         | 2379:2379                                                                       | etcd         | `Etcd` `http api` 服务端口号，供客户端交互使用。 此项目中我们只在 `Docker` 内部容器之间访问使用，所以也可以不暴露端口号给宿主机                                                                                                                                      |
| mysql        | 3306:3306                                                                       | mysql        | `Mysql` 服务默认端口号，宿主机可通过 `127.0.0.1:3306` 进行数据库的连接                                                                                                                                                                                               |
| redis        | 6379:6379                                                                       | redis        | `Redis` 服务默认端口号，宿主机可通过 `127.0.0.1:6379` 进行数据库的连接                                                                                                                                                                                               |
| mysql-manage | 1000:80                                                                         | mysql-manage | `phpMyAdmin` `web` 服务端口号，可以在宿主机 `127.0.0.1:1000` 访问                                                                                                                                                                                                    |
| redis-manage | 2000:80                                                                         | redis-manage | `phpRedisAdmin` `web` 服务端口号，可以在宿主机 `127.0.0.1:2000` 访问                                                                                                                                                                                                 |
| etcd-manage  | 7000:8080                                                                       | etcd-manage  | `Etcd Manage` `web` 服务端口号，可以在宿主机 `127.0.0.1:7000` 访问                                                                                                                                                                                                   |
| prometheus   | 3000:9090                                                                       | prometheus   | `Prometheus` `web` 服务端口号，可以在宿主机 `127.0.0.1:3000` 访问                                                                                                                                                                                                    |
| grafana      | 4000:3000                                                                       | grafana      | `Grafana` `web` 服务端口号，可以在宿主机 `127.0.0.1:4000` 访问                                                                                                                                                                                                       |
| jaeger       | 5000:16686                                                                      | jaeger       | `Jaeger` `web` 服务端口号，可以在宿主机 `127.0.0.1:5000` 访问                                                                                                                                                                                                        |
