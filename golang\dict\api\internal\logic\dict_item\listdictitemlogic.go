package dict_item

import (
	"context"
	"dict_item_rpc/dict_item"
	"fmt"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListDictItemLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 字典项列表
func NewListDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListDictItemLogic {
	return &ListDictItemLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListDictItemLogic) ListDictItem(req *types.ListDictItemReq) (resp *types.ListDictItemResp, err error) {
	// todo: add your logic here and delete this line
	// 调用dict_item服务
	respRpc, err := l.svcCtx.DictItemRpc.ListDictItem(l.ctx, &dict_item.ListDictItemReq{
		Page:     req.Page,
		PageSize: req.PageSize,
	})
	if err != nil {
		return nil, fmt.Errorf("调用dict_item服务的ListDictItem方法失败: %v", err)
	}
	var dataList []types.DictItem
	for _, v := range respRpc.List {
		dataList = append(dataList, types.DictItem{
			Id:          v.Id,
			DictId:      v.DictId,
			CategoryId:  v.CategoryId,
			Code:        v.Code,
			Name:        v.Name,
			Status:      v.Status,
			CreatedTime: v.CreatedTime,
			UpdatedTime: v.UpdatedTime,
		})
	}
	return &types.ListDictItemResp{
		Total:   respRpc.Total,
		List:    dataList,
		Message: "获取字典项列表成功",
	}, nil
}
