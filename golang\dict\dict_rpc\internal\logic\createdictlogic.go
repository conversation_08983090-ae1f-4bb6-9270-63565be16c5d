package logic

import (
	"context"
	"fmt"

	"dict_rpc/dict"
	"dict_rpc/internal/svc"
	"dict_rpc/model"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDictLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateDictLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDictLogic {
	return &CreateDictLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 创建字典
func (l *CreateDictLogic) CreateDict(in *dict.CreateDictReq) (*dict.CreateDictResp, error) {
	// todo: add your logic here and delete this line
	// 参数验证
	if in.Name == "" {
		return &dict.CreateDictResp{
			Message: "字典名称不能为空",
		}, nil
	}

	// 先查询数据库 防止重复
	data, err := l.svcCtx.DictModel.FindByName(in.Name)

	if err != nil {
		return nil, fmt.Errorf("查询数据失败,%v", err)
	}

	if data != nil {
		return nil, fmt.Errorf("字典名称已存在")
	}

	// 插入数据
	err = l.svcCtx.DictModel.Create(&model.Dict{
		Code:   in.Code,
		Name:   in.Name,
		Remark: in.Remark,
		Status: in.Status,
	})

	if err != nil {
		return nil, fmt.Errorf("插入数据失败: %v", err)
	}

	// 查询数据库 返回数据
	data, err = l.svcCtx.DictModel.FindByName(in.Name)

	if err != nil {
		return nil, fmt.Errorf("查询数据失败: %v", err)
	}

	return &dict.CreateDictResp{
		Id:      data.ID,
		Message: "创建字典成功",
	}, nil
}
