package dict_category

import (
	"context"
	"dict_category_rpc/dict_category"
	"fmt"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDictCategoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 创建字典分类
func NewCreateDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDictCategoryLogic {
	return &CreateDictCategoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateDictCategoryLogic) CreateDictCategory(req *types.CreateDictCategoryReq) (resp *types.CreateDictCategoryResp, err error) {
	// todo: add your logic here and delete this line
	// 调用dict_category服务
	respRpc, err := l.svcCtx.DictCategoryRpc.CreateDictCategory(l.ctx, &dict_category.CreateDictCategoryReq{
		DictId: req.DictId,
		Name:   req.Name,
		Status: req.Status,
	})
	if err != nil {
		return nil, fmt.Errorf("调用dict_category服务的CreateDictCategory方法失败: %v", err)
	}

	return &types.CreateDictCategoryResp{
		Id:      respRpc.Id,
		Message: "创建字典分类成功",
	}, nil
}
