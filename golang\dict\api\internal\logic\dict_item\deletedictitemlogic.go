package dict_item

import (
	"context"
	"dict_item_rpc/dict_item"
	"fmt"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDictItemLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 删除字典项
func NewDeleteDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDictItemLogic {
	return &DeleteDictItemLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteDictItemLogic) DeleteDictItem(req *types.DeleteDictItemReq) (resp *types.DeleteDictItemResp, err error) {
	// todo: add your logic here and delete this line
	// 调用dict_item服务
	respRpc, err := l.svcCtx.DictItemRpc.DeleteDictItem(l.ctx, &dict_item.DeleteDictItemReq{
		Id: req.Id,
	})

	if err != nil {
		return nil, fmt.Errorf("调用dict_item服务的DeleteDictItem方法失败: %v", err)
	}
	return &types.DeleteDictItemResp{
		Success: respRpc.Success,
		Message: "删除字典项成功",
	},nil
}
