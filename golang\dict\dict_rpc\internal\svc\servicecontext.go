package svc

import (
	"dict_rpc/internal/config"
	"dict_rpc/model"
)

type ServiceContext struct {
	Config config.Config
	DictModel model.DictModel
	DictCategoryRpc dictcategoryservice.DictCategoryService
	DictItemRpc     dictitemservice.DictItemService
}

func NewServiceContext(c config.Config) *ServiceContext {
	return &ServiceContext{
		Config: c,
		DictModel: *model.NewDictModel(model.NewDb(c.Mysql.DataSource)),
		DictCategoryRpc: dictcategoryservice.NewDictCategoryService(zrpc.MustNewClient(c.DictCategoryRpc)),
		DictItemRpc:     dictitemservice.NewDictItemService(zrpc.MustNewClient(c.DictItemRpc)),
	}
}
