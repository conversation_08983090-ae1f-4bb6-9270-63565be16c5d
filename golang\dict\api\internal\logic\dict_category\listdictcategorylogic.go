package dict_category

import (
	"context"
	"dict_category_rpc/dict_category"
	"fmt"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListDictCategoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 字典分类列表
func NewListDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListDictCategoryLogic {
	return &ListDictCategoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ListDictCategoryLogic) ListDictCategory(req *types.ListDictCategoryReq) (resp *types.ListDictCategoryResp, err error) {
	// todo: add your logic here and delete this line
	// 调用dict_category服务
	respRpc, err := l.svcCtx.DictCategoryRpc.ListDictCategory(l.ctx, &dict_category.ListDictCategoryReq{
		Page:     req.Page,
		PageSize: req.PageSize,
	})

	if err != nil {
		return nil, fmt.Errorf("调用dict_category服务的ListDictCategory方法失败: %v", err)
	}

	var dataList []types.DictCategory
	for _, v := range respRpc.List {
		dataList = append(dataList, types.DictCategory{
			Id:          v.Id,
			DictId:      v.DictId,
			Name:        v.Name,
			Status:      v.Status,
			CreatedTime: v.CreatedTime,
			UpdatedTime: v.UpdatedTime,
		})
	}

	return &types.ListDictCategoryResp{
		Total:   respRpc.Total,
		List:    dataList,
		Message: "获取字典分类列表成功",
	}, nil
}
