package dict_category

import (
	"context"
	"dict_category_rpc/dict_category"
	"fmt"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDictCategoryLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 获取字典分类详情
func NewGetDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDictCategoryLogic {
	return &GetDictCategoryLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDictCategoryLogic) GetDictCategory(req *types.GetDictCategoryReq) (resp *types.GetDictCategoryResp, err error) {
	// todo: add your logic here and delete this line
	// 调用dict_category服务
	respRpc, err := l.svcCtx.DictCategoryRpc.GetDictCategory(l.ctx, &dict_category.GetDictCategoryReq{
		Id: req.Id,
	})

	if err != nil {
		return nil, fmt.Errorf("调用dict_category服务的GetDictCategory方法失败: %v", err)
	}

	return &types.GetDictCategoryResp{
		Category: types.DictCategory{
			Id:          respRpc.Category.Id,
			DictId:      respRpc.Category.DictId,
			Name:        respRpc.Category.Name,
			Status:      respRpc.Category.Status,
			CreatedTime: respRpc.Category.CreatedTime,
			UpdatedTime: respRpc.Category.UpdatedTime,
		},
		Message: "获取字典分类详情成功",
	}, nil
}
