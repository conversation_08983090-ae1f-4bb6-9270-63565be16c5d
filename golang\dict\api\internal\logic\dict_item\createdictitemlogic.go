package dict_item

import (
	"context"
	"dict_item_rpc/dict_item"
	"fmt"

	"api/internal/svc"
	"api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDictItemLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

// 创建字典项
func NewCreateDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDictItemLogic {
	return &CreateDictItemLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateDictItemLogic) CreateDictItem(req *types.CreateDictItemReq) (resp *types.CreateDictItemResp, err error) {
	// todo: add your logic here and delete this line
	// 调用dict_item服务
	respRpc, err := l.svcCtx.DictItemRpc.CreateDictItem(l.ctx, &dict_item.CreateDictItemReq{
		DictId:     req.DictId,
		CategoryId: req.CategoryId,
		Code:       req.Code,
		Name:       req.Name,
		Status:     req.Status,
	})

	if err != nil {
		return nil, fmt.Errorf("调用dict_item服务的CreateDictItem方法失败: %v", err)
	}
	return &types.CreateDictItemResp{
		Id:      respRpc.Id,
		Message: "创建字典项成功",
	}, nil
}
