package logic

import (
	"context"

	"dict_item_rpc/dict_item"
	"dict_item_rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type ListDictItemLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewListDictItemLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ListDictItemLogic {
	return &ListDictItemLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 字典项列表
func (l *ListDictItemLogic) ListDictItem(in *dict_item.ListDictItemReq) (*dict_item.ListDictItemResp, error) {
	// todo: add your logic here and delete this line

	return &dict_item.ListDictItemResp{}, nil
}
