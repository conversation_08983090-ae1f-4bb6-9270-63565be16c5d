package logic

import (
	"context"

	"dict_category_rpc/dict_category"
	"dict_category_rpc/internal/svc"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDictCategoryLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetDictCategoryLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDictCategoryLogic {
	return &GetDictCategoryLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 获取字典分类详情
func (l *GetDictCategoryLogic) GetDictCategory(in *dict_category.GetDictCategoryReq) (*dict_category.GetDictCategoryResp, error) {
	// todo: add your logic here and delete this line

	return &dict_category.GetDictCategoryResp{}, nil
}
