// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5

package types

type BaseResponse struct {
	Id       int64  `json:"id"`
	Username string `json:"username"`
	Email    string `json:"email"`
	Phone    string `json:"phone,optional"`
	Token    string `json:"token"`
}

type LoginRequest struct {
	Username string `json:"username" validate:"required"`
	Password string `json:"password" validate:"required"`
}

type RegisterRequest struct {
	Username string `json:"username" validate:"required"`
	Password string `json:"password" validate:"required"`
	Email    string `json:"email" validate:"required,email"`
	Phone    string `json:"phone,optional"`
}

type UserResponse struct {
	BaseResponse BaseResponse `json:"base_response"`
	Code         int          `json:"code"`
	Message      string       `json:"message"`
}
