syntax = "proto3";

package dict_category;

option go_package = "./dict_category";

// 字典分类信息
message DictCategory {
  int64 id = 1;
  int64 dict_id = 2;
  string name = 3;
  int32 status = 4;
  string created_time = 5;
  string updated_time = 6;
}

// 创建字典分类请求
message CreateDictCategoryReq {
  int64 dict_id = 1;
  string name = 2;
  int32 status = 3;
}

// 创建字典分类响应
message CreateDictCategoryResp {
  int64 id = 1;
  string message = 2;
}

// 更新字典分类请求
message UpdateDictCategoryReq {
  int64 id = 1;
  int64 dict_id = 2;
  string name = 3;
  int32 status = 4;
}

// 更新字典分类响应
message UpdateDictCategoryResp {
  bool success = 1;
  string message = 2;
}

// 删除字典分类请求
message DeleteDictCategoryReq {
  int64 id = 1;
}

// 删除字典分类响应
message DeleteDictCategoryResp {
  bool success = 1;
  string message = 2;
}

// 获取字典分类详情请求
message GetDictCategoryReq {
  int64 id = 1;
}

// 获取字典分类详情响应
message GetDictCategoryResp {
  DictCategory category = 1;
  string message = 2;
}

// 字典分类列表请求
message ListDictCategoryReq {
  int32 page = 1;
  int32 page_size = 2;
  string name = 3; // 模糊查询
  int32 status = 4; // -1:全部, 0:禁用, 1:启用
  int64 dict_id = 5; // 指定字典ID
}

// 字典分类列表响应
message ListDictCategoryResp {
  int64 total = 1;
  repeated DictCategory list = 2;
  string message = 3;
}

// 字典分类服务
service DictCategoryService {
  // 创建字典分类
  rpc CreateDictCategory(CreateDictCategoryReq) returns(CreateDictCategoryResp);
  // 更新字典分类
  rpc UpdateDictCategory(UpdateDictCategoryReq) returns(UpdateDictCategoryResp);
  // 删除字典分类
  rpc DeleteDictCategory(DeleteDictCategoryReq) returns(DeleteDictCategoryResp);
  // 获取字典分类详情
  rpc GetDictCategory(GetDictCategoryReq) returns(GetDictCategoryResp);
  // 字典分类列表
  rpc ListDictCategory(ListDictCategoryReq) returns(ListDictCategoryResp);
} 